import React, { useState } from 'react';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';
import '../../styles/components/user-dashboard/Transactions.css';

// Import assets
// import cardCoinIcon from '../../assets/card-coin.png';

// Types
interface StakeTransaction {
  id: number;
  type: string;
  duration: string;
  from: string;
  amount: string;
  date: string;
}

interface WithdrawalTransaction {
  id: number;
  type: string;
  to: string;
  amount: string;
  date: string;
}

type Transaction = StakeTransaction | WithdrawalTransaction;

const Transactions: React.FC = () => {
  const [activeTab, setActiveTab] = useState('transactions');
  const [showBalances, setShowBalances] = useState(true);
  const [selectedWallet, setSelectedWallet] = useState('Wallet 1 (90,000 stake)');
  const [activeTransactionTab, setActiveTransactionTab] = useState('stakes');
  const [timeFilter, setTimeFilter] = useState('all-time');

  // Sample transaction data
  const stakesData = [
    { id: 1, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
    { id: 2, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
    { id: 3, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
    { id: 4, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
    { id: 5, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
    { id: 6, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
    { id: 7, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
    { id: 8, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
    { id: 9, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
    { id: 10, type: 'ESVC Staked', duration: '6 Months', from: 'From 0x6d4...bF1', amount: '900 ESVC', date: 'Today, 10:17 AM' },
  ];

  const withdrawalsData = [
    { id: 1, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
    { id: 2, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
    { id: 3, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
    { id: 4, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
    { id: 5, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
    { id: 6, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
    { id: 7, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
    { id: 8, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
    { id: 9, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
    { id: 10, type: 'ROI Withdrawn', to: 'To 0x6d4...bF1', amount: '-1,600 USDC', date: 'Today, 10:17 AM' },
  ];

  const currentData = activeTransactionTab === 'stakes' ? stakesData : withdrawalsData;
  const totalValue = activeTransactionTab === 'stakes' ? '11,302' : '11,302';

  const toggleBalances = () => {
    setShowBalances(!showBalances);
  };

  const formatAmount = (amount: string) => {
    return showBalances ? amount : '****';
  };

  return (
    <UserDashboardLayout className="transactions-container">
      <div className="transactions-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosin 👋</h1>
            <p className="greeting-subtitle">Here is your transaction history</p>

            <div className="header-controls">
              <button className="stake-esvc-btn">
                <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                Stake ESVC
              </button>

              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-layout">
          <UserSideNav activeTab={activeTab} onTabChange={setActiveTab} />

          <div className="dashboard-content">
            <div className="transactions-section">
              {/* Wallet Selector */}
              <div className="wallet-selector">
                <select
                  className="wallet-dropdown"
                  value={selectedWallet}
                  onChange={(e) => setSelectedWallet(e.target.value)}
                >
                  <option value="Wallet 1 (90,000 stake)">🔗 Wallet 1 (90,000 stake)</option>
                  <option value="Wallet 2 (45,000 stake)">🔗 Wallet 2 (45,000 stake)</option>
                  <option value="Wallet 3 (25,000 stake)">🔗 Wallet 3 (25,000 stake)</option>
                </select>
              </div>

              {/* Transaction Tabs */}
              <div className="transaction-tabs">
                <button
                  className={`tab-button ${activeTransactionTab === 'stakes' ? 'active' : ''}`}
                  onClick={() => setActiveTransactionTab('stakes')}
                >
                  Stakes
                </button>
                <button
                  className={`tab-button ${activeTransactionTab === 'withdrawals' ? 'active' : ''}`}
                  onClick={() => setActiveTransactionTab('withdrawals')}
                >
                  Withdrawals
                </button>
              </div>

              {/* Time Filter and Total */}
              <div className="transaction-header">
                <select
                  className="time-filter"
                  value={timeFilter}
                  onChange={(e) => setTimeFilter(e.target.value)}
                >
                  <option value="all-time">📅 All time</option>
                  <option value="last-month">📅 Last month</option>
                  <option value="last-week">📅 Last week</option>
                </select>

                <div className="total-value">
                  <span className="total-label">TOTAL VALUE (BASED ON FILTER DATE)</span>
                  <span className="total-amount">{formatAmount(totalValue)}</span>
                </div>
              </div>

              {/* Transaction List */}
              <div className="transaction-list">
                {currentData.map((transaction, index) => (
                  <div key={transaction.id} className="transaction-item">
                    <div className="transaction-number">{index + 1}.</div>
                    <div className="transaction-icon">
                      <div className="transaction-avatar">
                        <span className="avatar-text">ES</span>
                      </div>
                    </div>
                    <div className="transaction-details">
                      <div className="transaction-type">
                        {transaction.type} • {transaction.duration || ''}
                      </div>
                      <div className="transaction-address">
                        {transaction.from || transaction.to}
                      </div>
                    </div>
                    <div className="transaction-amount">
                      <div className="amount">{formatAmount(transaction.amount)}</div>
                      <div className="date">{transaction.date}</div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              <div className="pagination">
                <button className="pagination-btn">← Previous</button>
                <div className="pagination-numbers">
                  <span className="page-number active">1</span>
                  <span className="page-number">2</span>
                  <span className="page-number">3</span>
                  <span className="page-number">4</span>
                  <span className="page-dots">...</span>
                  <span className="page-number">10</span>
                </div>
                <button className="pagination-btn">Next →</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default Transactions;
